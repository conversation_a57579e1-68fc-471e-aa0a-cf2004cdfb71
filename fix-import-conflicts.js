#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Components that conflict between Next.js and Lucide
const CONFLICT_COMPONENTS = {
  'Link': {
    nextjs: 'next/link',
    lucide: 'lucide-react'
  },
  'Image': {
    nextjs: 'next/image', 
    lucide: 'lucide-react'
  }
};

function findAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      findAllFiles(fullPath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
      fileList.push(fullPath);
    }
  });
  
  return fileList;
}

function fixConflictsInFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  let updatedContent = content;
  let fixes = [];
  
  // Check for conflicts
  Object.keys(CONFLICT_COMPONENTS).forEach(componentName => {
    const conflict = CONFLICT_COMPONENTS[componentName];
    
    // Check if file has both Next.js and Lucide imports of the same component
    const hasNextJsImport = new RegExp(`import\\s+${componentName}\\s+from\\s+['"]${conflict.nextjs}['"]`).test(content);
    const hasLucideImport = new RegExp(`import\\s*\\{[^}]*\\b${componentName}\\b[^}]*\\}\\s*from\\s*['"]${conflict.lucide}['"]`).test(content);
    
    if (hasNextJsImport && hasLucideImport) {
      // Remove the conflicting component from Lucide import
      const lucideImportRegex = /import\s*\{([^}]+)\}\s*from\s*['"]lucide-react['"];?/;
      const match = updatedContent.match(lucideImportRegex);
      
      if (match) {
        const importList = match[1]
          .split(',')
          .map(name => name.trim())
          .filter(name => name !== componentName && name.length > 0);
        
        if (importList.length > 0) {
          const newImportLine = `import { ${importList.join(', ')} } from 'lucide-react';`;
          updatedContent = updatedContent.replace(lucideImportRegex, newImportLine);
        } else {
          // Remove the entire Lucide import if no components left
          updatedContent = updatedContent.replace(/import\s*\{[^}]*\}\s*from\s*['"]lucide-react['"];?\n?/, '');
        }
        
        fixes.push(`Removed ${componentName} from Lucide imports (conflicts with Next.js)`);
      }
    }
  });
  
  if (fixes.length === 0) {
    return { fixed: false, reason: 'No conflicts found' };
  }
  
  fs.writeFileSync(filePath, updatedContent, 'utf8');
  
  return {
    fixed: true,
    filePath: filePath.replace(process.cwd() + '/', ''),
    fixes
  };
}

function main() {
  console.log('🔍 Scanning for Next.js/Lucide component conflicts...\n');
  
  const projectRoot = process.cwd();
  const allFiles = findAllFiles(projectRoot);
  
  let totalFixed = 0;
  const results = [];
  
  allFiles.forEach(filePath => {
    const result = fixConflictsInFile(filePath);
    if (result.fixed) {
      totalFixed++;
      results.push(result);
      
      console.log(`✅ Fixed: ${result.filePath}`);
      result.fixes.forEach(fix => {
        console.log(`   ${fix}`);
      });
      console.log();
    }
  });
  
  if (totalFixed === 0) {
    console.log('✨ No conflicts found!');
  } else {
    console.log(`\n🎉 Successfully fixed conflicts in ${totalFixed} files.`);
  }
}

main();
