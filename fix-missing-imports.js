#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// All possible Lucide components that might be used
const ALL_LUCIDE_COMPONENTS = [
  'Activity', 'Airplay', 'AlertCircle', 'AlertOctagon', 'AlertTriangle', 'AlignCenter', 'AlignJustify',
  'AlignLeft', 'AlignRight', 'Anchor', 'Aperture', 'Archive', 'ArrowDown', 'ArrowDownCircle',
  'ArrowDownLeft', 'ArrowDownRight', 'ArrowLeft', 'ArrowLeftCircle', 'ArrowRight', 'ArrowRightCircle',
  'ArrowUp', 'ArrowUpCircle', 'ArrowUpLeft', 'ArrowUpRight', 'AtSign', 'Award', 'BarChart', 'BarChart2',
  'BarChart3', 'Battery', 'BatteryCharging', 'BatteryFull', 'BatteryLow', 'Bell', 'BellOff', 'Bluetooth',
  'Bold', 'Book', 'BookOpen', 'Bookmark', 'Box', 'Briefcase', 'Calendar', 'Camera', 'CameraOff',
  'Car', 'Cast', 'Check', 'CheckCircle', 'CheckCircle2', 'CheckSquare', 'ChevronDown', 'ChevronLeft',
  'ChevronRight', 'ChevronUp', 'ChevronsDown', 'ChevronsLeft', 'ChevronsRight', 'ChevronsUp', 'Chrome',
  'Circle', 'Clipboard', 'Clock', 'Cloud', 'CloudDrizzle', 'CloudLightning', 'CloudRain', 'CloudSnow',
  'Code', 'Coffee', 'Columns', 'Command', 'Compass', 'Copy', 'CornerDownLeft', 'CornerDownRight',
  'CornerLeftDown', 'CornerLeftUp', 'CornerRightDown', 'CornerRightUp', 'CornerUpLeft', 'CornerUpRight',
  'Cpu', 'CreditCard', 'Crop', 'Crosshair', 'Crown', 'Database', 'Delete', 'Disc', 'DollarSign',
  'Download', 'DownloadCloud', 'Droplet', 'Edit', 'Edit2', 'Edit3', 'ExternalLink', 'Eye', 'EyeOff',
  'Facebook', 'FastForward', 'Feather', 'File', 'FileText', 'Film', 'Filter', 'Flag', 'Folder',
  'FolderOpen', 'FolderPlus', 'Framer', 'Frown', 'Gift', 'GitBranch', 'GitCommit', 'GitMerge',
  'GitPullRequest', 'GitHub', 'Gitlab', 'Globe', 'Grid', 'HardDrive', 'Hash', 'Headphones', 'Heart',
  'HelpCircle', 'Hexagon', 'Home', 'Image', 'Inbox', 'Info', 'Instagram', 'Italic', 'Key', 'Layers',
  'Layout', 'LifeBuoy', 'Link', 'Link2', 'Linkedin', 'List', 'Loader', 'Lock', 'LogIn', 'LogOut',
  'Mail', 'Map', 'MapPin', 'Maximize', 'Maximize2', 'Meh', 'Menu', 'MessageCircle', 'MessageSquare',
  'Mic', 'MicOff', 'Minimize', 'Minimize2', 'Minus', 'MinusCircle', 'MinusSquare', 'Monitor', 'Moon',
  'MoreHorizontal', 'MoreVertical', 'MousePointer', 'Move', 'Music', 'Navigation', 'Navigation2',
  'Octagon', 'Package', 'Paperclip', 'Pause', 'PauseCircle', 'PenTool', 'Percent', 'Phone', 'PhoneCall',
  'PhoneForwarded', 'PhoneIncoming', 'PhoneMissed', 'PhoneOff', 'PhoneOutgoing', 'PieChart', 'Play',
  'PlayCircle', 'Plus', 'PlusCircle', 'PlusSquare', 'Pocket', 'Power', 'Printer', 'Radio', 'RefreshCw',
  'RefreshCcw', 'Repeat', 'Repeat1', 'Rewind', 'RotateCcw', 'RotateCw', 'Rss', 'Save', 'Scissors',
  'Search', 'Send', 'Server', 'Settings', 'Share', 'Share2', 'Shield', 'ShieldOff', 'ShoppingBag',
  'ShoppingCart', 'Shuffle', 'Sidebar', 'SkipBack', 'SkipForward', 'Slack', 'Slash', 'Sliders',
  'Smartphone', 'Smile', 'Speaker', 'Square', 'Star', 'StopCircle', 'Sun', 'Sunrise', 'Sunset',
  'Tablet', 'Tag', 'Target', 'Terminal', 'Thermometer', 'ThumbsDown', 'ThumbsUp', 'ToggleLeft',
  'ToggleRight', 'Tool', 'Trash', 'Trash2', 'Triangle', 'Truck', 'Tv', 'Twitter', 'Type', 'Umbrella',
  'Underline', 'Unlock', 'Upload', 'UploadCloud', 'User', 'UserCheck', 'UserMinus', 'UserPlus',
  'UserX', 'Users', 'Video', 'VideoOff', 'Volume', 'Volume1', 'Volume2', 'VolumeX', 'Watch', 'Wifi',
  'WifiOff', 'Wind', 'X', 'XCircle', 'XSquare', 'Youtube', 'Zap', 'ZapOff', 'ZoomIn', 'ZoomOut',
  
  // Additional common ones
  'Alarm', 'AlarmClock', 'AlertTriangle', 'Ambulance', 'Apple', 'Archive', 'Bike', 'Building',
  'Building2', 'Bus', 'Calendar', 'CalendarDays', 'Camera', 'Candle', 'Carrot', 'CloudSun',
  'Coffee', 'ContactRound', 'Cup', 'Diamond', 'Dice6', 'Divide', 'Droplets', 'Factory',
  'Flame', 'Flashlight', 'FolderOpen', 'Gamepad2', 'Gem', 'GraduationCap', 'Hospital',
  'Infinity', 'Keyboard', 'Lamp', 'Laptop', 'Lightbulb', 'MailOpen', 'Mailbox', 'Megaphone',
  'Milk', 'Mouse', 'Music2', 'Newspaper', 'Paintbrush2', 'Palette', 'PartyPopper', 'Pencil',
  'Pin', 'Pizza', 'Plane', 'PlayPause', 'PlaySquare', 'Playing', 'Plug', 'Rainbow', 'Rocket',
  'Ruler', 'Sailboat', 'Sandwich', 'Satellite', 'School', 'Scissors', 'Scooter', 'ScrollText',
  'Ship', 'Snowflake', 'Sparkles', 'Store', 'Timer', 'Tornado', 'Tractor', 'Train', 'TrendingDown',
  'TrendingUp', 'Trophy', 'Umbrella', 'Waves', 'Wheat'
];

function findAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      findAllFiles(fullPath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.jsx') || file.endsWith('.js')) {
      fileList.push(fullPath);
    }
  });
  
  return fileList;
}

function extractLucideImports(content) {
  const importMatch = content.match(/import\s*\{([^}]+)\}\s*from\s*['"]lucide-react['"];?/);
  if (!importMatch) return [];
  
  return importMatch[1]
    .split(',')
    .map(name => name.trim())
    .filter(name => name.length > 0);
}

function findLucideComponentsInContent(content) {
  const components = new Set();
  
  // Find all Lucide component usage patterns
  ALL_LUCIDE_COMPONENTS.forEach(component => {
    const patterns = [
      new RegExp(`<${component}\\s+`, 'g'),  // <Component ...
      new RegExp(`<${component}\\s*/>`, 'g'), // <Component />
      new RegExp(`<${component}>`, 'g'),      // <Component>
    ];
    
    patterns.forEach(pattern => {
      if (pattern.test(content)) {
        components.add(component);
      }
    });
  });
  
  return Array.from(components);
}

function fixImportsInFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Find components used in the file
  const usedComponents = findLucideComponentsInContent(content);
  
  if (usedComponents.length === 0) {
    return { fixed: false, reason: 'No Lucide components found' };
  }
  
  // Get currently imported components
  const currentImports = extractLucideImports(content);
  
  // Find missing imports
  const missingImports = usedComponents.filter(comp => !currentImports.includes(comp));
  
  if (missingImports.length === 0) {
    return { fixed: false, reason: 'All imports already present' };
  }
  
  // Combine all needed imports
  const allImports = [...new Set([...currentImports, ...usedComponents])].sort();
  
  let updatedContent = content;
  
  if (currentImports.length > 0) {
    // Update existing import
    const newImportLine = `import { ${allImports.join(', ')} } from 'lucide-react';`;
    updatedContent = updatedContent.replace(
      /import\s*\{[^}]+\}\s*from\s*['"]lucide-react['"];?/,
      newImportLine
    );
  } else {
    // Add new import after other imports
    const importLines = updatedContent.match(/^import .+;$/gm) || [];
    if (importLines.length > 0) {
      const lastImportIndex = updatedContent.lastIndexOf(importLines[importLines.length - 1]);
      const endOfLastImport = lastImportIndex + importLines[importLines.length - 1].length;
      const newImportLine = `\nimport { ${allImports.join(', ')} } from 'lucide-react';`;
      updatedContent = updatedContent.slice(0, endOfLastImport) + newImportLine + updatedContent.slice(endOfLastImport);
    } else {
      // No imports found, add at the top
      const newImportLine = `import { ${allImports.join(', ')} } from 'lucide-react';\n`;
      updatedContent = newImportLine + updatedContent;
    }
  }
  
  fs.writeFileSync(filePath, updatedContent, 'utf8');
  
  return {
    fixed: true,
    filePath: filePath.replace(process.cwd() + '/', ''),
    missingImports,
    totalImports: allImports.length
  };
}

function main() {
  console.log('🔍 Scanning for missing Lucide component imports...\n');
  
  const projectRoot = process.cwd();
  const allFiles = findAllFiles(projectRoot);
  
  let totalFixed = 0;
  let totalMissingImports = 0;
  const results = [];
  
  allFiles.forEach(filePath => {
    const result = fixImportsInFile(filePath);
    if (result.fixed) {
      totalFixed++;
      totalMissingImports += result.missingImports.length;
      results.push(result);
      
      console.log(`✅ Fixed: ${result.filePath}`);
      console.log(`   Added imports: ${result.missingImports.join(', ')}`);
      console.log(`   Total imports: ${result.totalImports}\n`);
    }
  });
  
  if (totalFixed === 0) {
    console.log('✨ No missing imports found!');
  } else {
    console.log(`\n🎉 Successfully fixed ${totalMissingImports} missing imports across ${totalFixed} files.`);
    console.log('\n📊 Summary:');
    results.forEach(result => {
      console.log(`  ${result.filePath}: +${result.missingImports.length} imports`);
    });
  }
}

main();
