#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Valid Lucide React component names (subset of commonly used ones)
const VALID_LUCIDE_COMPONENTS = [
  'AlarmClock', 'AlertCircle', 'AlertTriangle', 'Ambulance', 'Apple', 'Archive', 
  'ArrowDown', 'ArrowDownLeft', 'ArrowDownRight', 'ArrowLeft', 'ArrowLeftRight', 
  'ArrowRight', 'ArrowUp', 'ArrowUpDown', 'ArrowUpLeft', 'ArrowUpRight', 'Bar<PERSON>hart', 
  'Battery', 'BatteryLow', 'Bell', 'BellOff', 'Bike', 'Book', 'BookOpen', 'Building', 
  'Building2', 'Bus', 'Calendar', 'CalendarDays', 'Camera', 'Candle', 'Car', 'Carrot', 
  'Check', 'CheckCircle', 'ChevronDown', 'ChevronLeft', 'ChevronRight', 'ChevronUp', 
  'Circle', 'Clipboard', 'Clock', '<PERSON>', 'CloudLightning', '<PERSON>Rain', 'CloudSnow', 
  'CloudSun', 'Coffee', 'Compass', 'ContactRound', 'Cup', 'Diamond', 'Dice6', 'Disc', 
  'Divide', 'Droplets', 'Factory', 'FileText', 'Filter', 'Flame', 'Flashlight', 
  'Folder', 'FolderOpen', 'Gamepad2', 'Gem', 'Globe', 'GraduationCap', 'Headphones', 
  'Heart', 'HelpCircle', 'Home', 'Inbox', 'Infinity', 'Keyboard', 'Lamp', 'Laptop', 
  'Leaf', 'Lightbulb', 'Loader2', 'Lock', 'Mail', 'MailOpen', 'Mailbox', 'Map', 
  'MapPin', 'Megaphone', 'MessageCircle', 'MessageSquare', 'Mic', 'Milk', 'Minus', 
  'Monitor', 'Moon', 'Mountain', 'Mouse', 'Music', 'Music2', 'Newspaper', 'Package', 
  'Paintbrush2', 'Palette', 'PartyPopper', 'Pause', 'Pen', 'PenTool', 'Pencil', 
  'Phone', 'Pin', 'Pizza', 'Plane', 'Play', 'PlayPause', 'PlaySquare', 'Playing', 
  'Plug', 'Plus', 'Power', 'Printer', 'Radio', 'Rainbow', 'Repeat', 'Repeat1', 
  'Rocket', 'RotateCcw', 'RotateCw', 'Ruler', 'Sailboat', 'Sandwich', 'Satellite', 
  'Save', 'School', 'Scissors', 'Scooter', 'ScrollText', 'Search', 'Send', 'Settings', 
  'Shirt', 'Ship', 'Shuffle', 'SkipBack', 'SkipForward', 'Smartphone', 'Snowflake', 
  'Sparkles', 'Square', 'Star', 'Stethoscope', 'Store', 'Sun', 'Sunrise', 'Sunset', 
  'Target', 'Timer', 'Tornado', 'Tractor', 'Train', 'TrendingDown', 'TrendingUp', 
  'Triangle', 'Trophy', 'Truck', 'Tv', 'Umbrella', 'User', 'Users', 'Utensils', 
  'Video', 'Volume1', 'Volume2', 'VolumeX', 'Watch', 'Waves', 'Wheat', 'Wind', 
  'X', 'XSquare', 'Zap'
];

function findTsxFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      findTsxFiles(fullPath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(fullPath);
    }
  });
  
  return fileList;
}

function fixMalformedLucideImports(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Find lucide-react import statements
  const importRegex = /import\s*\{\s*([^}]+)\s*\}\s*from\s*['"]lucide-react['"];?/g;
  let match;
  let hasIssues = false;
  let updatedContent = content;
  
  while ((match = importRegex.exec(content)) !== null) {
    const fullImport = match[0];
    const importList = match[1];
    
    // Parse the import list and clean it up
    const cleanedComponents = [];
    
    // Split by comma and clean each component
    const rawComponents = importList.split(',');
    
    for (let component of rawComponents) {
      // Remove comments and extra whitespace
      component = component.replace(/\/\/.*$/gm, '').trim();
      
      // Skip empty components
      if (!component) continue;
      
      // Check if it's a valid Lucide component name (starts with capital letter, alphanumeric only)
      if (/^[A-Z][a-zA-Z0-9]*$/.test(component) && VALID_LUCIDE_COMPONENTS.includes(component)) {
        cleanedComponents.push(component);
      } else {
        console.log(`  Removing invalid component: "${component}"`);
        hasIssues = true;
      }
    }
    
    if (hasIssues) {
      // Remove duplicates and sort
      const uniqueComponents = [...new Set(cleanedComponents)].sort();
      
      // Create new import statement
      const newImport = `import { ${uniqueComponents.join(', ')} } from 'lucide-react';`;
      
      // Replace the old import
      updatedContent = updatedContent.replace(fullImport, newImport);
    }
  }
  
  if (hasIssues) {
    fs.writeFileSync(filePath, updatedContent, 'utf8');
    return {
      fixed: true,
      filePath: filePath.replace(process.cwd() + '/', '')
    };
  }
  
  return { fixed: false };
}

function main() {
  console.log('🔧 Scanning for malformed Lucide import statements...\n');
  
  const projectRoot = process.cwd();
  const tsxFiles = findTsxFiles(projectRoot);
  
  let totalFixed = 0;
  const results = [];
  
  tsxFiles.forEach(filePath => {
    const result = fixMalformedLucideImports(filePath);
    if (result.fixed) {
      totalFixed++;
      results.push(result);
      console.log(`✅ Fixed: ${result.filePath}`);
    }
  });
  
  if (totalFixed === 0) {
    console.log('✨ No malformed imports found!');
  } else {
    console.log(`\n🎉 Fixed ${totalFixed} files with malformed Lucide imports.`);
    console.log('\nFiles fixed:');
    results.forEach(result => {
      console.log(`  ${result.filePath}`);
    });
  }
}

main();
