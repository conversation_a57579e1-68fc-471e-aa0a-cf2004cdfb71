"use client";

import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  Gamepad2,
  BookOpen,
  Users,
  Award,
  BarChart3,
  Target,
  Headphones,
  PenTool,
  FileText,
  Zap,
  Star,
  ArrowRight,
  Play,
  CheckCircle,
  Sparkles,
  Trophy,
  Clock,
  Globe,
  Brain, // Added for AI-powered insights
  Lightbulb, // For innovation/solutions
  Hourglass, // For time-saving
  Rocket // For rapid progress/engagement
} from 'lucide-react';
import Footer from '../components/layout/Footer';
import SEOWrapper from '../components/seo/SEOWrapper';
import { getFAQSchema } from '../lib/seo/structuredData';

// Animated text data for hero section - Teacher-focused benefits
const heroTextVariations = [
  { text: "Boost Student Engagement", color: "text-blue-600" },
  { text: "Uncover Real-time Progress", color: "text-green-600" },
  { text: "Gain Predictive Insights", color: "text-purple-600" },
  { text: "Streamline Assignments", color: "text-indigo-600" },
  { text: "Tailor Learning to Every Student", color: "text-emerald-600" },
  { text: "Excel in GCSE-Style Exams", color: "text-orange-600" }
];

// Animated text hook (no changes needed to the hook itself)
function useTypewriter(texts: typeof heroTextVariations, speed = 100) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const current = texts[currentIndex];
    const fullText = current.text;

    const timer = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (currentText.length < fullText.length) {
          setCurrentText(fullText.slice(0, currentText.length + 1));
        } else {
          // Pause before deleting
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        // Deleting
        if (currentText.length > 0) {
          setCurrentText(fullText.slice(0, currentText.length - 1));
        } else {
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % texts.length);
        }
      }
    }, isDeleting ? speed / 2 : speed);

    return () => clearTimeout(timer);
  }, [currentText, isDeleting, currentIndex, texts, speed]);

  return {
    text: currentText,
    color: texts[currentIndex].color,
    isDeleting
  };
}

export default function Home() {
  const { text: animatedText, color: textColor } = useTypewriter(heroTextVariations);

  // FAQ data for structured data (updated for teacher focus)
  const faqs = [
    {
      question: "How does LanguageGems support teachers and improve student outcomes?",
      answer: "LanguageGems provides a comprehensive teacher dashboard with real-time analytics, predictive insights, and automated assignment tools. This saves teachers time, identifies struggling students early, and boosts engagement through gamified, curriculum-aligned content, ultimately leading to better GCSE results."
    },
    {
      question: "Which languages and exam boards does LanguageGems support?",
      answer: "We currently support Spanish, French, and German, with comprehensive vocabulary and grammar content aligned with GCSE-style requirements for exam boards like AQA and Edexcel. Our content is designed to prepare students for success in these examinations."
    },
    {
      question: "What is the pricing structure for schools and educational institutions?",
      answer: "We offer transparent annual pricing designed for schools, starting from £399 for basic access, with our comprehensive school plan at £699/year. This includes unlimited teachers and students, ensuring no hidden costs and scalable learning for your entire department."
    }
  ];

  const faqStructuredData = getFAQSchema(faqs);

  // Feature categories for the new homepage
  const gameCategories = [
    {
      title: "Vocabulary Mastery",
      icon: Sparkles,
      color: "from-blue-500 to-indigo-600",
      description: "Our diverse range of vocabulary games ensures students not only learn new words but retain them long-term.",
      games: [
        { name: "VocabMaster", description: "Intelligent spaced repetition for lasting retention" },
        { name: "Word Blast", description: "Fast-paced word recognition challenges" },
        { name: "Memory Match", description: "Boost memory with engaging vocabulary pairs" },
        { name: "Hangman", description: "Classic word guessing for core vocabulary" }
      ]
    },
    {
      title: "Sentence Construction & Translation",
      icon: PenTool,
      color: "from-green-500 to-emerald-600",
      description: "Beyond individual words, our games build confidence in forming and translating complete sentences.",
      games: [
        { name: "Sentence Sprint", description: "Drag & drop sentence building for fluency" },
        { name: "Translation Tycoon", description: "Business-themed translation practice" },
        { name: "Word Scramble", description: "Unscramble letters to form correct words" }
      ]
    },
    {
      title: "Grammar & Conjugation",
      icon: Target,
      color: "from-purple-500 to-violet-600",
      description: "Master complex grammatical structures and verb conjugations through interactive battles and quests.",
      games: [
        { name: "Conjugation Duel", description: "RPG-style Spanish verb conjugation battles" },
        { name: "Verb Quest", description: "Engaging quests to master grammar rules" }
      ]
    },
    {
      title: "Listening Comprehension",
      icon: Headphones,
      color: "from-orange-500 to-red-600",
      description: "Develop crucial listening skills with audio-based games designed to improve recognition and understanding.",
      games: [
        { name: "Detective Listening Game", description: "Solve cases by identifying words via audio" }
      ]
    }
  ];

  // Updated assessment types for teacher benefits
  const assessmentTypes = [
    {
      title: "GCSE-Style Exam Practice", // Changed from "Exams by difficulty"
      description: "Prepare students for success with official-style questions aligned with GCSE Foundation & Higher tiers, providing invaluable exam readiness.",
      icon: Award,
      features: ["GCSE-style Reading Tests", "GCSE-style Listening Tests", "Writing Tasks with guided prompts", "Speaking Practice prompts & recordings"]
    },
    {
      title: "Reading Comprehension Analytics", // Emphasize analytics
      description: "Automated analysis of multi-language texts provides instant feedback and identifies comprehension gaps, saving teachers marking time.",
      icon: BookOpen,
      features: ["Age-appropriate, diverse texts", "Multiple question formats", "Automated marking & feedback", "Detailed progress tracking"]
    },
    {
      title: "Precision Dictation Assessments", // More active title
      description: "Improve listening and writing accuracy with audio-to-text practice at variable speeds, pinpointing specific phonetic and spelling weaknesses.",
      icon: Headphones,
      features: ["Normal & slow speed playback", "Foundation & Higher difficulty", "Instant error highlighting", "Targeted remedial practice"]
    },
    {
      title: "Topic & Skill-Based Diagnostics", // More descriptive title
      description: "Focused practice on specific curriculum themes and grammatical skills. Understand student strengths and weaknesses by theme, topic, and vocabulary.",
      icon: FileText,
      features: ["Organised by GCSE themes", "Targeted vocabulary & grammar topics", "Identifies weak areas automatically", "Reinforces specific skills"]
    }
  ];

  // Updated platform features for teacher benefits and USPs
  const platformFeatures = [
    {
      title: "Unrivaled Student Engagement",
      description: "15+ interactive games, a cross-game XP system, and 50+ achievements keep students deeply motivated and actively learning.",
      icon: Rocket, // Changed icon for engagement
      stat: "Deep Engagement"
    },
    {
      title: "Predictive Analytics & AI Insights",
      description: "Identify struggling students before they fall behind with real-time, AI-powered insights into individual and class performance.",
      icon: Brain, // Added icon for AI
      stat: "Early Intervention"
    },
    {
      title: "Streamlined Assignment Management",
      description: "Create custom assignments in minutes with reusable templates and auto-grading, saving teachers hours of administrative work.",
      icon: Hourglass, // Added icon for time-saving
      stat: "Time-Saving Automation"
    },
    {
      title: "GCSE Curriculum Aligned",
      description: "Comprehensive content aligned with GCSE-style requirements for exam boards like AQA and Edexcel, ready to deploy for your classes.", // Adjusted wording
      icon: Target,
      stat: "Full GCSE Coverage"
    }
  ];

  // New section for "Why Choose LanguageGems?"
  const differentiators = [
    {
      title: "Comprehensive Gamification Ecosystem",
      description: "Beyond simple games, our cross-game XP system, 50+ achievements, streak tracking, and power-ups drive consistent student motivation and engagement.",
      icon: Trophy
    },
    {
      title: "AI-Powered Predictive Analytics",
      description: "Identify and address student struggles *before* they happen. Get real-time data, word-level difficulty analysis, and optimal assignment suggestions for every student and class.",
      icon: BarChart3
    },
    {
      title: "Sophisticated Assignment Automation",
      description: "Save hours with customizable assignment templates, auto-grading with detailed feedback, flexible vocabulary selection modes, and real-time progress tracking.",
      icon: Zap
    },
    {
      title: "Pinpoint Strengths & Weaknesses",
      description: "Our dashboard provides deep insights into student and class performance by theme, topic, and vocabulary, allowing you to easily spot and address specific learning gaps.",
      icon: Lightbulb
    },
    {
      title: "Unified Teacher Workflow Optimization",
      description: "Manage all classes, assignments, and student progress from one intuitive dashboard, designed to streamline your daily teaching tasks and enhance efficiency.",
      icon: Users
    },
    {
      title: "Multi-Language & Custom Content Support",
      description: "Seamlessly teach Spanish, French, and German with audio integration, multiple game types, and the ability to integrate your own custom vocabulary.",
      icon: Globe
    }
  ];


  return (
    <SEOWrapper structuredData={faqStructuredData}>
      <div className="flex min-h-screen flex-col">
        <main className="flex-grow">
          {/* Hero Section */}
          <div className="w-full relative min-h-[90vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            <div className="absolute inset-0 bg-[url('/images/homepage/subtle-pattern.svg')] opacity-5"></div>

            <div className="container mx-auto px-6 z-10 py-20">
              <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 items-center">
                <div className="lg:col-span-6 text-center lg:text-left">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="mb-6"
                  >
                    <span className="inline-block bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold mb-4">
                      🎓 For Language Teachers & Departments
                    </span>
                  </motion.div>

                  <motion.h1
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 }}
                    className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-800 leading-tight mb-6"
                  >
                    LanguageGems:
                    <span className={`block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600 min-h-[1.2em] ${textColor}`}>
                      {animatedText}
                      <span className="animate-pulse">|</span>
                    </span>
                  </motion.h1>

                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="text-lg md:text-xl text-slate-600 mb-8 leading-relaxed max-w-3xl mx-auto lg:mx-0"
                  >
                    Empower your students and streamline your teaching with the most advanced
                    GCSE language platform, combining engaging gamification with powerful AI-driven insights.
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="flex flex-col sm:flex-row gap-4 mb-8 justify-center lg:justify-start"
                  >
                    <Link href="/contact-sales" className="inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl px-8 py-4 text-lg shadow-lg hover:shadow-xl transform transition-all hover:scale-105">
                      <Clock className="mr-2 h-5 w-5" />
                      Book a Free Demo
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Link>

                    <Link href="/auth/signup" className="inline-flex items-center justify-center bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-xl px-8 py-4 text-lg shadow-lg hover:shadow-xl transform transition-all hover:scale-105">
                      <Users className="mr-2 h-5 w-5" />
                      Start Your Free School Trial
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Link>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="flex items-center gap-8 text-sm text-slate-500 justify-center lg:justify-start"
                  >
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>AI-Powered Insights</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Globe className="w-4 h-4 text-blue-500" />
                      <span>3 Languages</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Trophy className="w-4 h-4 text-purple-500" />
                      <span>GCSE Curriculum Aligned</span>
                    </div>
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="lg:col-span-6 flex justify-center relative"
                >
                  <div className="relative w-full max-w-lg">
                    {/* CONSIDER REPLACING THIS ENTIRE IMAGE BLOCK */}
                    {/* RECOMMENDATION: A composite image showing both engaged students AND a glimpse of the teacher dashboard/analytics */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-3xl transform rotate-6 opacity-20"></div>
                    <div className="relative bg-white rounded-3xl p-8 shadow-2xl">
                      <Image
                        src="/images/homepage/hero.png" // **UPDATE THIS PATH TO YOUR NEW IMAGE**
                        alt="LanguageGems teacher dashboard showing student progress analytics and students engaged in interactive language games." // **UPDATE THIS ALT TEXT**
                        width={600} // Increased width for better detail
                        height={450} // Increased height
                        priority
                        className="w-full h-auto rounded-2xl"
                      />
                    </div>

                    {/* Floating Feature Cards - Rephrased for teacher benefits */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.5 }}
                      className="absolute -top-4 -left-4 bg-white rounded-xl p-4 shadow-lg border border-slate-100 max-w-48"
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                          <Brain className="w-4 h-4 text-green-600" /> {/* Changed icon */}
                        </div>
                        <span className="text-sm font-semibold text-slate-800">AI-Powered Insights</span>
                      </div>
                      <p className="text-xs text-slate-600">Spot student weaknesses early</p>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.6 }}
                      className="absolute -bottom-4 -right-4 bg-white rounded-xl p-4 shadow-lg border border-slate-100 max-w-48"
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="w-4 h-4 text-blue-600" /> {/* Changed icon */}
                        </div>
                        <span className="text-sm font-semibold text-slate-800">Automated Assignments</span>
                      </div>
                      <p className="text-xs text-slate-600">Save hours with auto-grading</p>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Subtle decorative elements */}
            <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-200 to-indigo-200 rounded-full opacity-60 animate-float"></div>
            <div className="absolute bottom-20 right-10 w-16 h-16 bg-gradient-to-br from-purple-200 to-pink-200 rounded-full opacity-60 animate-float-delayed"></div>
            <div className="absolute top-1/2 left-5 w-12 h-12 bg-gradient-to-br from-green-200 to-emerald-200 rounded-full opacity-60 animate-float-slow"></div>
          </div>

          {/* Platform Overview Section */}
          <div className="py-20 bg-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl md:text-4xl font-bold text-slate-800 mb-4"
                >
                  LanguageGems: Your All-in-One Solution for Modern Language Education
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="text-lg text-slate-600 max-w-3xl mx-auto"
                >
                  More than just games, LanguageGems combines engaging student experiences with powerful teacher tools designed to simplify your workflow and maximize learning outcomes.
                </motion.p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                {platformFeatures.map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="text-center"
                  >
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="text-3xl font-bold text-blue-600 mb-2">{feature.stat}</div>
                    <h3 className="text-xl font-bold text-slate-800 mb-4">{feature.title}</h3>
                    <p className="text-slate-600">{feature.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Games Section */}
          <div className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl md:text-4xl font-bold text-slate-800 mb-4"
                >
                  Engage Every Student: Our Interactive Learning Games
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="text-lg text-slate-600 max-w-3xl mx-auto"
                >
                  Our comprehensive gamification ecosystem, featuring over 15 dynamic games, ensures students are constantly motivated and actively learning, turning practice into play.
                </motion.p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {gameCategories.map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <div className={`w-12 h-12 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-4`}>
                      <category.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-slate-800 mb-2">{category.title}</h3>
                    <p className="text-slate-600 text-sm mb-4">{category.description}</p>
                    <div className="space-y-2">
                      {category.games.map((game, gameIndex) => (
                        <div key={gameIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                          <div>
                            <div className="font-medium text-slate-800 text-sm">{game.name}</div>
                            <div className="text-xs text-slate-600">{game.description}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-center mt-12"
              >
                <Link
                  href="/games-demo" // Link to a general games demo page
                  className="inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl px-8 py-4 text-lg shadow-lg hover:shadow-xl transform transition-all hover:scale-105"
                >
                  <Play className="mr-2 h-5 w-5" />
                  Explore Interactive Games
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </motion.div>
            </div>
          </div>

          {/* Assessments Section */}
          <div className="py-20 bg-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl md:text-4xl font-bold text-slate-800 mb-4"
                >
                  Streamline Evaluation: Comprehensive Assessment Tools
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="text-lg text-slate-600 max-w-3xl mx-auto"
                >
                  Gain precise insights into student mastery with automated, curriculum-aligned assessments. Our tools save you time while providing detailed feedback for every student.
                </motion.p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {assessmentTypes.map((assessment, index) => (
                  <motion.div
                    key={assessment.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300"
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mb-6">
                      <assessment.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-slate-800 mb-4">{assessment.title}</h3>
                    <p className="text-slate-600 mb-6">{assessment.description}</p>
                    <div className="space-y-2">
                      {assessment.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                          <span className="text-sm text-slate-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-center mt-12"
              >
                <Link
                  href="/assessments-demo" // Link to a general assessments demo page or book a demo
                  className="inline-flex items-center justify-center bg-gradient-to-r from-green-600 to-emerald-600 text-white font-semibold rounded-xl px-8 py-4 text-lg shadow-lg hover:shadow-xl transform transition-all hover:scale-105"
                >
                  <FileText className="mr-2 h-5 w-5" />
                  Request Assessment Feature Walkthrough
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </motion.div>
            </div>
          </div>

          {/* Teacher Tools Section - CRITICAL SECTION FOR TEACHERS */}
          <div className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="mb-6">
                    <span className="inline-block bg-gradient-to-r from-emerald-600 to-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      🏫 For Department Heads & Teachers
                    </span>
                  </div>

                  <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-6">
                    The Most Comprehensive Teacher Dashboard in Language Learning.
                  </h2>

                  <p className="text-lg text-slate-600 mb-8 leading-relaxed">
                    From predictive analytics to automated assignment management, LanguageGems offers a unified dashboard designed to give you complete control and valuable insights, saving you hours of administrative work. Our dashboard pinpoints **student strengths and weaknesses by theme, topic, and vocabulary**, enabling truly targeted intervention.
                  </p>

                  <div className="space-y-4 mb-8">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <BarChart3 className="w-4 h-4 text-green-600" />
                      </div>
                      <span className="text-slate-700 font-medium">Real-time Predictive Analytics: Spot student weaknesses early.</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <PenTool className="w-4 h-4 text-blue-600" />
                      </div>
                      <span className="text-slate-700 font-medium">Sophisticated Assignment Automation: Save hours with auto-grading.</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <Target className="w-4 h-4 text-purple-600" />
                      </div>
                      <span className="text-slate-700 font-medium">Customizable & GCSE Curriculum-Aligned Content.</span>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link href="/auth/signup" className="inline-flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl px-6 py-3 shadow-lg hover:shadow-xl transform transition-all hover:scale-105">
                      <Users className="mr-2 h-4 w-4" />
                      Start Your Free School Trial
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Link>

                    <Link href="/contact-sales" className="inline-flex items-center justify-center bg-white text-slate-700 font-semibold rounded-xl px-6 py-3 border border-slate-300 hover:bg-slate-50 transition-all">
                      <Clock className="mr-2 h-4 w-4" />
                      Book a Custom Demo
                    </Link>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="relative"
                >
                  <div className="bg-white rounded-2xl p-8 shadow-2xl">
                    {/* THIS IS WHERE YOUR TEACHER DASHBOARD SCREENSHOT/VIDEO SHOULD GO */}
                    <div className="bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl p-12 flex items-center justify-center min-h-[300px]"> {/* Added min-height as a visual placeholder */}
                      <div className="text-center">
                        <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg mx-auto mb-4">
                          <BarChart3 className="h-8 w-8 text-indigo-600" />
                        </div>
                        <p className="text-slate-700 font-medium">Teacher Dashboard Preview</p>
                        <p className="text-sm text-slate-500 mt-1">Analytics & Assignment Tools</p>
                        <p className="text-xs text-red-500 mt-2">**Replace this with a screenshot/video of your actual dashboard!**</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>

          {/* Why Choose LanguageGems? (Strong Differentiators) */}
          <div className="py-20 bg-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl md:text-4xl font-bold text-slate-800 mb-4"
                >
                  Why Choose LanguageGems? Unmatched Innovation for Language Departments
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="text-lg text-slate-600 max-w-3xl mx-auto"
                >
                  We are building the future of language education, providing tools that genuinely transform student engagement and teacher efficiency.
                </motion.p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"> {/* Changed to 3 columns for better spacing with 6 items */}
                {differentiators.map((diff, index) => (
                  <motion.div
                    key={diff.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mb-4">
                      <diff.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-slate-800 mb-2">{diff.title}</h3>
                    <p className="text-slate-600 text-sm">{diff.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>


        </main>

        <Footer />
      </div>
    </SEOWrapper>
  );
}